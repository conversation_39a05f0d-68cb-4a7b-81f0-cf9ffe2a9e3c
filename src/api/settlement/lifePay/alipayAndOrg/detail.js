import request from "@/utils/request";

// 支付宝与机构账单-明细数据
export default {
  // 列表查询
  list(data) {
    return request({
      url: "/st/lifePay/aliPayOrg/queryPage",
      method: "post",
      data: data,
    });
  },
  // 导出
  export(data) {
    return request({
      url: "/st/lifePay/aliPayOrg/exportExcel",
      method: "post",
      data: data,
    });
  },
  // 导入
  batchImport(data) {
    return request({
      url: "/st/lifePay/aliPayOrg/importExcel",
      method: "post",
      data: data,
      headers: {
        "Content-Type": "multipart/form-data",
      },
      skipIntercept: true,
    });
  },

  // 明细数据
  detail(data) {
    return request({
      url: "/st/lifePay/aliPayOrg/detail",
      method: "post",
      data: data,
    });
  },
};
