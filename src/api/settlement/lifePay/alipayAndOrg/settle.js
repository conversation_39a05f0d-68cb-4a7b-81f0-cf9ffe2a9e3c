import request from "@/utils/request";

// 支付宝与机构账单-结算单
export default {
  // 列表查询
  list(data) {
    return request({
      url: "/st/lifePay/aliPayOrg/settlement/queryPage",
      method: "post",
      data: data,
    });
  },
  // 导出
  export(data) {
    return request({
      url: "/st/lifePay/aliPayOrg/settlement/exportExcel",
      method: "post",
      data: data,
    });
  },
  // 批量生成收据单
  batchGenerate(data) {
    return request({
      url: "/st/lifePay/aliPayOrg/settlement/batchGenerate",
      method: "post",
      data: data,
    });
  },
  // 查看
  preview(data) {
    return request({
      url: "/st/lifePay/aliPayOrg/settlement/preview",
      method: "post",
      data: data,
    });
  },
  // 下载
  download(data) {
    return request({
      url: "/st/lifePay/aliPayOrg/settlement/download",
      method: "post",
      data: data,
    });
  },
  // 更新
  update(data) {
    return request({
      url: "/st/lifePay/aliPayOrg/settlement/edit",
      method: "post",
      data: data,
    });
  },
};
