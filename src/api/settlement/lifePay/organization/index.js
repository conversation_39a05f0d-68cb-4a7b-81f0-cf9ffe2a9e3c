import request from "@/utils/request";

// 机构信息控制层
export default {
  // 分页查询
  list(data) {
    return request({
      url: "/st/lifePay/institInfo/queryPage",
      method: "post",
      data: data,
    });
  },
  // 导出Excel
  export(data) {
    return request({
      url: "/st/lifePay/institInfo/exportExcel",
      method: "post",
      data: data,
    });
  },
  // 导入Excel
  import(data) {
    return request({
      url: "/st/lifePay/institInfo/importExcel",
      method: "post",
      data: data,
      headers: {
        "Content-Type": "multipart/form-data",
      },
      skipIntercept: true,
    });
  },
  // 编辑
  update(data) {
    return request({
      url: "/st/lifePay/institInfo/edit",
      method: "post",
      data: data,
    });
  },
  // 上传文件
  uploadFile(data) {
    return request({
      url: "/st/lifePay/institInfo/uploadFile",
      method: "post",
      data: data,
    });
  },
  // 获取下拉列表
  getDropLists() {
    return request({
      url: "/st/lifePay/institInfo/getDropLists",
      method: "get",
    });
  },
};
