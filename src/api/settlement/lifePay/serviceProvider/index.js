import request from "@/utils/request";

// 服务商信息控制层
export default {
  // 分页查询
  list(data) {
    return request({
      url: "/st/lifePay/providerInfo/queryPage",
      method: "post",
      data: data,
    });
  },
  // 导出Excel
  export(data) {
    return request({
      url: "/st/lifePay/providerInfo/exportExcel",
      method: "post",
      data: data,
    });
  },
  // 导入Excel
  import(data) {
    return request({
      url: "/st/lifePay/providerInfo/importExcel",
      method: "post",
      data: data,
      headers: {
        "Content-Type": "multipart/form-data",
      },
      skipIntercept: true,
    });
  },
  // 获取下拉列表
  getDropLists() {
    return request({
      url: "/st/lifePay/providerInfo/getDropLists",
      method: "get",
    });
  },
  // 新增
  add(data) {
    return request({
      url: "/st/lifePay/providerInfo/add",
      method: "post",
      data: data,
    });
  },
  // 编辑
  update(data) {
    return request({
      url: "/st/lifePay/providerInfo/edit",
      method: "post",
      data: data,
    });
  },
  // 删除
  delete(data) {
    return request({
      url: "/st/lifePay/providerInfo/delete",
      method: "post",
      data: data,
    });
  },
};
