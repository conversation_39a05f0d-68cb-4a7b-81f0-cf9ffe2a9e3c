import request from "@/utils/request";

// 公服相关数据-开票信息管理
export default {
  // 列表
  list(data) {
    return request({
      url: "/workHour/record/pageList",
      method: "post",
      data: data,
    });
  },
  // 导出
  export(data) {
    return request({
      url: "/workHour/record/export",
      method: "post",
      data: data,
    });
  },
  // 导入
  batchImport(data) {
    return request({
      url: "/workHour/record/importExcel",
      method: "post",
      data: data,
      headers: {
        "Content-Type": "multipart/form-data",
      },
      skipIntercept: true,
    });
  },
  // 详情
  detail(data) {
    return request({
      url: "/workHour/record/detail",
      method: "post",
      data: data,
    });
  },
  // 更新
  update(data) {
    return request({
      url: "/workHour/record/update",
      method: "post",
      data: data,
    });
  },
};
