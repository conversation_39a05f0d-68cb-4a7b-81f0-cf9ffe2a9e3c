<template>
  <div class="luckysheet-container">
    <div class="luckysheet-toolbar" v-if="showToolbar">
      <slot name="toolbar">
        <!-- <el-button type="primary" size="mini" @click="importExcel"
          >导入Excel</el-button
        >
        <el-button
          type="success"
          size="mini"
          @click="exportExcel"
          :disabled="!luckysheetLoaded"
          >导出Excel</el-button
        >
        <input
          ref="fileInput"
          type="file"
          accept=".xlsx"
          style="display: none"
          @change="handleFileChange"
        /> -->
      </slot>
    </div>
    <div :id="containerId" class="luckysheet-content"></div>
  </div>
</template>

<script>
// 使用 CDN 引入 LuckySheet，不再通过 import 导入依赖
// 导入配置文件
import {
  defaultHeaders,
  defaultColumnConfig,
  createEmptySheetData,
} from "./config";

export default {
  name: "LuckySheetComponent",
  props: {
    // 使用v-model绑定的值
    value: {
      type: Array,
      default: () => [],
    },
    // 表格容器ID，确保在同一页面有多个表格时不冲突
    containerId: {
      type: String,
      default: "luckysheet",
    },
    // 是否显示工具栏
    showToolbar: {
      type: Boolean,
      default: true,
    },
    // 表头配置
    headers: {
      type: Array,
      default: () => [],
    },
    // 列宽配置
    columnConfig: {
      type: Object,
      default: () => ({}),
    },
  },
  data() {
    return {
      luckysheetLoaded: false,
      globalEventListener: null,
      observer: null,
      isUpdatingFromSync: false, // 标记是否正在从同步方法更新数据
      // 使用导入的配置
      defaultHeaders,
      defaultColumnConfig,
    };
  },
  computed: {
    // 获取实际使用的表头
    actualHeaders() {
      return this.headers.length > 0 ? this.headers : this.defaultHeaders;
    },
    // 获取实际使用的列宽配置
    actualColumnConfig() {
      return Object.keys(this.columnConfig).length > 0
        ? this.columnConfig
        : this.defaultColumnConfig;
    },
  },
  mounted() {
    this.loadLuckysheetScripts();
  },
  beforeDestroy() {
    // 销毁实例，避免内存泄漏
    if (window.luckysheet) {
      window.luckysheet.destroy();
    }

    // 断开MutationObserver连接
    if (this.observer) {
      this.observer.disconnect();
    }

    // 移除全局事件监听器
    if (this.globalEventListener) {
      document.removeEventListener("click", this.globalEventListener, true);
    }
  },
  methods: {
    // 动态加载LuckySheet脚本和样式
    loadLuckysheetScripts() {
      // 动态加载LuckySheet脚本和样式
      const linkCss1 = document.createElement("link");
      linkCss1.rel = "stylesheet";
      linkCss1.href =
        "https://cdn.jsdelivr.net/npm/luckysheet/dist/plugins/css/pluginsCss.css";
      document.head.appendChild(linkCss1);

      const linkCss2 = document.createElement("link");
      linkCss2.rel = "stylesheet";
      linkCss2.href =
        "https://cdn.jsdelivr.net/npm/luckysheet/dist/plugins/plugins.css";
      document.head.appendChild(linkCss2);

      const linkCss3 = document.createElement("link");
      linkCss3.rel = "stylesheet";
      linkCss3.href =
        "https://cdn.jsdelivr.net/npm/luckysheet/dist/css/luckysheet.css";
      document.head.appendChild(linkCss3);

      // 添加字体图标文件
      const linkCss4 = document.createElement("link");
      linkCss4.rel = "stylesheet";
      linkCss4.href =
        "https://cdn.jsdelivr.net/npm/luckysheet/dist/assets/iconfont/iconfont.css";
      document.head.appendChild(linkCss4);

      // 添加额外的字体图标
      const linkCss5 = document.createElement("link");
      linkCss5.rel = "stylesheet";
      linkCss5.href = "https://at.alicdn.com/t/font_2142986_kxw7n4vf8zs.css";
      document.head.appendChild(linkCss5);

      const script1 = document.createElement("script");
      script1.src =
        "https://cdn.jsdelivr.net/npm/luckysheet/dist/plugins/js/plugin.js";
      script1.onload = () => {
        const script2 = document.createElement("script");
        script2.src =
          "https://cdn.jsdelivr.net/npm/luckysheet/dist/luckysheet.umd.js";
        script2.onload = () => {
          this.initLuckysheet();
        };
        document.head.appendChild(script2);
      };
      document.head.appendChild(script1);
    },

    // 初始化LuckySheet
    initLuckysheet() {
      if (window.luckysheet) {
        try {
          // 将表格数据转换为LuckySheet格式
          const sheetData = this.convertToLuckysheetData(this.value || []);

          // 在初始化前，添加全局事件监听器来阻止表单提交
          this.addGlobalEventListener();

          // 处理数据格式
          let processedData;

          if (Array.isArray(sheetData)) {
            // 如果是数组，检查是否已经是sheet对象数组
            if (sheetData.length > 0 && sheetData[0].name !== undefined) {
              // 已经是sheet对象数组，直接使用
              processedData = sheetData;
            } else {
              // 是普通数组，转换为sheet对象
              processedData = [
                {
                  name: "Sheet1",
                  color: "",
                  status: 1,
                  order: 0,
                  data: sheetData,
                  config: {},
                  index: 0,
                },
              ];
            }
          } else if (typeof sheetData === "object" && sheetData !== null) {
            // 单个sheet对象，包装成数组
            processedData = [sheetData];
          } else {
            // 无效数据，创建空表格
            processedData = [
              {
                name: "Sheet1",
                color: "",
                status: 1,
                order: 0,
                data: [[]],
                config: {},
                index: 0,
              },
            ];
          }

          // 确保每个sheet都有必要的属性
          processedData = processedData.map((sheet) => {
            const validSheet = { ...sheet };
            if (!validSheet.name) validSheet.name = "Sheet1";
            if (!validSheet.color) validSheet.color = "";
            if (!validSheet.status) validSheet.status = 1;
            if (!validSheet.order) validSheet.order = 0;
            if (!validSheet.data) validSheet.data = [[]];
            if (!validSheet.config) validSheet.config = {};
            if (typeof validSheet.index === "undefined") validSheet.index = 0;

            return validSheet;
          });

          // 默认配置
          const options = {
            container: this.containerId,
            lang: "zh",
            data: processedData,
            showinfobar: false,
            // 以下配置与demo示例保持一致
            showToolbar: true, // 启用工具栏
            showFormulaBar: true, // 启用公式栏
            showSheetTabs: true, // 启用底部sheet页
            showConfig: true, // 启用工作表配置
            enableCopy: true, // 允许复制
            enableSelection: true, // 允许选择
            editable: true, // 允许编辑
            rowAddable: true, // 允许添加行
            colAddable: true, // 允许添加列
            hook: {
              // 当单元格更新时
              cellUpdated: (r, c, value) => {
                console.log("Cell updated:", r, c, value);

                // 延迟执行，避免频繁触发数据同步
                setTimeout(() => {
                  // 不再自动计算分润配置
                  // 仅同步更新value
                  this.syncLuckysheetData();
                }, 300); // 使用更长的延迟，减少频繁更新
              },

              // 表格加载完成后的处理
              workbookCreateAfter: () => {
                try {
                  console.log("表格初始化加载完成");

                  // 检查初始数据是否为空，只有在空数据时才同步
                  // 这样可以避免在已有数据的情况下触发不必要的同步
                  const currentData = this.value;
                  if (
                    !currentData ||
                    (Array.isArray(currentData) && currentData.length === 0) ||
                    (typeof currentData === "object" &&
                      Object.keys(currentData).length === 0)
                  ) {
                    console.log("初始数据为空，执行首次同步");
                    // 延迟执行，避免初始化时就触发数据同步
                    setTimeout(() => {
                      this.syncLuckysheetData();
                    }, 300);
                  } else {
                    console.log("初始化时已有数据，跳过首次同步");
                  }
                } catch (error) {
                  console.error("表格加载完成处理出错:", error);
                }
              },
            },
          };

          console.log("LuckySheet配置:", options);

          // 创建表格
          window.luckysheet.create(options);

          // 在初始化完成后，为所有按钮添加事件监听器
          this.$nextTick(() => {
            this.preventButtonSubmit();
          });

          this.luckysheetLoaded = true;
        } catch (error) {
          console.error("Error initializing LuckySheet:", error);
          this.$message.error("初始化表格组件失败");
        }
      }
    },

    // 添加全局事件监听器
    addGlobalEventListener() {
      // 添加全局点击事件监听器
      document.addEventListener("click", this.handleGlobalClick, true);

      // 保存引用，以便在组件销毁时移除
      this.globalEventListener = this.handleGlobalClick;
    },

    // 全局点击事件处理函数
    handleGlobalClick(e) {
      // 检查点击的元素是否是 LuckySheet 中的按钮
      if (
        e.target &&
        (e.target.tagName === "BUTTON" ||
          e.target.closest("button") ||
          e.target.classList.contains("luckysheet-cell-input"))
      ) {
        // 检查元素是否在 LuckySheet 容器内
        const container = document.getElementById(this.containerId);
        if (
          container &&
          (container.contains(e.target) ||
            e.target.closest(`#${this.containerId}`))
        ) {
          console.log("LuckySheet button clicked, preventing form submission");

          // 检查是否是添加按钮
          const isAddButton =
            e.target.textContent === "添加" ||
            (e.target.closest("button") &&
              e.target.closest("button").textContent === "添加");

          // 如果是表单提交按钮，阻止默认行为
          if (
            e.target.type === "submit" ||
            e.target.closest('button[type="submit"]')
          ) {
            e.preventDefault();
          } else if (!isAddButton) {
            // 对于非添加按钮的其他按钮，阻止事件冒泡，防止触发表单提交
            e.stopPropagation();
          }

          // 延迟同步数据，让 LuckySheet 的内部操作先完成
          setTimeout(() => {
            this.syncLuckysheetData();
          }, 100);

          // 不返回 false，允许事件继续传播给 LuckySheet 内部处理
        }
      }
    },

    // 防止按钮提交表单
    preventButtonSubmit() {
      // 获取LuckySheet容器内的所有按钮
      const container = document.getElementById(this.containerId);
      if (!container) return;

      // 立即处理当前按钮
      this.processButtons(container);

      // 使用MutationObserver监听DOM变化
      const observer = new MutationObserver(() => {
        // DOM变化时处理按钮
        this.processButtons(container);
      });

      // 开始观察
      observer.observe(container, {
        childList: true,
        subtree: true,
      });

      // 保存observer引用，以便在组件销毁时断开连接
      this.observer = observer;
    },

    // 处理容器内的所有按钮
    processButtons(container) {
      // 查找所有按钮并添加事件监听器
      const buttons = container.querySelectorAll("button");
      buttons.forEach((button) => {
        // 检查是否是添加按钮
        const isAddButton = button.textContent === "添加";

        // 对于添加按钮，不添加事件监听器，让它保持原有功能
        if (isAddButton) {
          // 移除之前可能添加的事件监听器
          button.removeEventListener("click", this.handleButtonClick);
          return;
        }

        // 对于其他按钮，添加事件监听器
        button.removeEventListener("click", this.handleButtonClick);
        button.addEventListener("click", this.handleButtonClick);
      });
    },

    // 按钮点击事件处理函数
    handleButtonClick(e) {
      // 检查是否是添加按钮
      const isAddButton =
        e.target.textContent === "添加" ||
        (e.target.closest("button") &&
          e.target.closest("button").textContent === "添加");

      // 如果是表单提交按钮，阻止默认行为
      if (
        e.target.type === "submit" ||
        e.target.closest('button[type="submit"]')
      ) {
        e.preventDefault();
      } else if (!isAddButton) {
        // 对于非添加按钮的其他按钮，阻止事件冒泡，防止触发表单提交
        e.stopPropagation();
      }

      // 延迟同步数据，让 LuckySheet 的内部操作先完成
      setTimeout(() => {
        this.syncLuckysheetData();
      }, 100);

      // 不返回 false，允许事件继续传播给 LuckySheet 内部处理
    },

    // 将value转换为LuckySheet格式
    convertToLuckysheetData(data) {
      // 如果数据为空或不是预期的格式，创建一个带有默认表头的空表格
      if (!data || !Array.isArray(data) || data.length === 0) {
        // 使用导入的 createEmptySheetData 函数创建空表格
        return createEmptySheetData(
          this.actualHeaders,
          this.actualColumnConfig
        );
      }

      // 如果已有数据，直接返回原始数据
      return data;
    },

    // 从LuckySheet同步数据到value
    syncLuckysheetData() {
      if (!window.luckysheet) return;

      // 如果已经在更新中，避免循环调用
      if (this.isUpdatingFromSync) {
        console.log("已在更新中，跳过同步");
        return;
      }

      try {
        // 标记正在从同步方法更新数据
        this.isUpdatingFromSync = true;

        // 获取当前表格的所有数据
        const allSheets = window.luckysheet.getAllSheets();

        // 如果没有数据，返回空数组
        if (!allSheets || !Array.isArray(allSheets) || allSheets.length === 0) {
          this.$emit("input", []);
          return;
        }

        // 比较新旧数据，只有在数据真正变化时才触发更新
        const currentValue = JSON.stringify(this.value);
        const newValue = JSON.stringify(allSheets);

        if (currentValue !== newValue) {
          console.log("数据已变化，触发更新");
          // 直接将原始数据传递给父组件
          this.$emit("input", allSheets);
          console.log("同步LuckySheet数据完成");
        } else {
          console.log("数据未变化，跳过更新");
        }
      } catch (error) {
        console.error("同步LuckySheet数据出错:", error);
      } finally {
        // 确保在任何情况下都重置标记，使用更长的延迟
        setTimeout(() => {
          this.isUpdatingFromSync = false;
          console.log("重置更新标记");
        }, 100);
      }
    },

    // 计算分润配置 - 已禁用自动计算功能
    calculateProfitSharing(/* r, value */) {
      // 此方法已被禁用，不再自动计算分润配置
      // 保留方法签名以避免代码引用错误，注释掉未使用的参数
      console.log("分润配置自动计算功能已禁用");
    },

    // 导入Excel
    importExcel() {
      this.$refs.fileInput.click();
    },

    // 处理文件选择
    handleFileChange(e) {
      const file = e.target.files[0];
      if (!file) return;

      // 检查文件类型
      if (
        file.type !==
        "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
      ) {
        this.$message.error("请选择.xlsx格式的Excel文件");
        return;
      }

      // 加载LuckyExcel库
      const script = document.createElement("script");
      script.src =
        "https://cdn.jsdelivr.net/npm/luckyexcel/dist/luckyexcel.umd.js";
      script.onload = () => {
        // 读取文件
        const reader = new FileReader();
        reader.onload = (event) => {
          const data = event.target.result;
          window.LuckyExcel.transformExcelToLucky(data, (exportJson) => {
            try {
              console.log("Excel导入数据:", exportJson);

              // 加载到LuckySheet
              window.luckysheet.destroy();

              // 检查导入的数据结构
              if (
                !exportJson ||
                !exportJson.sheets ||
                !Array.isArray(exportJson.sheets)
              ) {
                throw new Error("导入的Excel数据格式不正确");
              }

              // 直接初始化LuckySheet
              window.luckysheet.create({
                container: this.containerId,
                lang: "zh",
                data: exportJson.sheets,
                title: file.name,
                showinfobar: false,
                showToolbar: true,
                showFormulaBar: true,
                showSheetTabs: true,
                showConfig: true,
                enableCopy: true,
                enableSelection: true,
                editable: true,
                rowAddable: true,
                colAddable: true,
                hook: {
                  cellUpdated: () => {
                    this.syncLuckysheetData();
                  },
                  workbookCreateAfter: () => {
                    this.syncLuckysheetData();
                  },
                },
              });

              this.$message.success("Excel文件导入成功");

              // 导入后同步数据
              this.syncLuckysheetData();
            } catch (error) {
              console.error("Excel导入错误:", error);
              this.$message.error(`Excel导入失败: ${error.message}`);
            }
          });
        };
        reader.readAsArrayBuffer(file);
      };
      document.head.appendChild(script);

      // 重置文件输入
      e.target.value = "";
    },

    // 导出Excel
    exportExcel() {
      if (!window.luckysheet) {
        this.$message.error("表格组件未初始化");
        return;
      }

      // 先同步数据
      this.syncLuckysheetData();

      // 创建一个简单的下载链接
      const blob = new Blob(
        [JSON.stringify(window.luckysheet.getAllSheets())],
        {
          type: "application/json",
        }
      );
      const url = URL.createObjectURL(blob);
      const link = document.createElement("a");
      link.href = url;
      link.download = "场站配置数据.json";
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      URL.revokeObjectURL(url);

      this.$message.success("数据导出成功");
    },
  },
  watch: {
    // 监听value变化，更新表格数据
    value: {
      handler(newVal, oldVal) {
        // 如果是从syncLuckysheetData方法触发的更新，则不处理
        if (this.isUpdatingFromSync) {
          console.log("从syncLuckysheetData触发的更新，跳过watcher处理");
          return;
        }

        // 比较新旧值，只有在真正变化时才更新
        const newValStr = JSON.stringify(newVal);
        const oldValStr = JSON.stringify(oldVal);

        if (newValStr === oldValStr) {
          console.log("value未发生实质变化，跳过更新");
          return;
        }

        console.log("value发生变化，重新初始化表格");

        if (this.luckysheetLoaded && window.luckysheet) {
          // 标记正在更新，防止触发循环
          this.isUpdatingFromSync = true;

          try {
            // 重新加载数据
            const sheetData = this.convertToLuckysheetData(newVal || []);
            window.luckysheet.destroy();

            // 直接初始化LuckySheet
            window.luckysheet.create({
              container: this.containerId,
              lang: "zh",
              data: Array.isArray(sheetData) ? sheetData : [sheetData],
              showinfobar: false,
              showToolbar: true,
              showFormulaBar: true,
              showSheetTabs: true,
              showConfig: true,
              enableCopy: true,
              enableSelection: true,
              editable: true,
              rowAddable: true,
              colAddable: true,
              hook: {
                cellUpdated: () => {
                  // 使用延迟避免频繁触发
                  setTimeout(() => {
                    this.syncLuckysheetData();
                  }, 300);
                },
                workbookCreateAfter: () => {
                  console.log("表格重新创建完成");
                  // 不再自动触发同步，避免初始化时的循环更新
                },
              },
            });
          } finally {
            // 确保标记被重置
            setTimeout(() => {
              this.isUpdatingFromSync = false;
              console.log("value watcher处理完成，重置更新标记");
            }, 300);
          }
        }
      },
      deep: true,
    },
  },
};
</script>

<style lang="less" scoped>
.luckysheet-container {
  position: relative;
  height: 500px;
  width: 100%;
  margin-bottom: 20px;
}

.luckysheet-toolbar {
  display: flex;
  justify-content: flex-end;
  margin-bottom: 10px;

  .el-button {
    margin-left: 10px;
  }
}

.luckysheet-content {
  width: 100%;
  height: 100%;
  min-height: 450px;
  border: 1px solid #dcdfe6;
}
/deep/ .luckysheet-cols-menu {
  z-index: 99999 !important;
}
</style>
