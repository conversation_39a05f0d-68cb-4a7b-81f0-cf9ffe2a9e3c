<template>
  <div>
    <vxe-table :data="fileList" align="center" ref="fileTable">
      <vxe-column field="fileType" title="文件类型">
        <template #default="{ row, $rowIndex }">
          <svg-icon
            :iconClass="'icon-' + getFileType(row[options.name])"
            :style="`width: 40px; height: 40px;`"
            className="common-icon"
            v-if="showFile(row[options.name] || row[options.url])"
          />
          <el-image
            v-else
            class="common-icon"
            :src="row[options.url]"
            :style="`width: 40px; height: 40px;`"
          ></el-image>
        </template>
      </vxe-column>
      <vxe-column field="fileName" title="文件名">
        <template #default="{ row, $rowIndex }">
          <el-link @click="handlePreview($rowIndex)">{{
            row[options.name]
          }}</el-link>
        </template>
      </vxe-column>
      <vxe-column title="操作">
        <template #default="{ row, $rowIndex }">
          <el-link @click="handleDownLoad(row)">下载</el-link>
        </template>
      </vxe-column>
    </vxe-table>
    <PreviewFiles
      :initial-index="previewIndex"
      v-if="showViewer"
      :on-close="
        () => {
          showViewer = false;
        }
      "
      :url-list="fileList"
      :fileOptions="options"
    />
  </div>
</template>

<script>
import { fileDownLoad } from "@/utils/downLoad.js";
import { downLoadUrl2Blob } from "@/api/common.js";
import PreviewFiles from "@/components/PreviewFiles/index.vue";

export default {
  components: { PreviewFiles },
  props: {
    fileList: {
      type: Array,
      default: () => [],
    },
    fileOptions: {
      type: Object,
      default: () => {
        return {};
      },
    },
  },
  data() {
    return {
      fileDict: "doc|docx|mp3|mp4|pdf|ppt|pptx|rar|txt|xls|xlsx|zip",
      imgDict: "jpg|png|jpeg",
      showViewer: false,
      previewIndex: 0,
      fileColumns: [
        { title: "文件类型", field: "fileType" },
        { title: "文件名称", field: "fileName" },
        { title: "操作", slot: { default: "operation" } },
      ],
    };
  },
  computed: {
    options() {
      return {
        url: "url",
        name: "name",
        ...this.fileOptions,
      };
    },
  },
  methods: {
    //获取文件类型
    getFileType(str) {
      // 提取后缀名并转小写
      const ext = str?.match(/\.([^.]+)$/)?.[1]?.toLowerCase() || "";
      // 将预设后缀列表转为数组
      const validExts = this.fileDict.split("|");
      // 严格匹配预设列表中的后缀
      return validExts.includes(ext) ? ext : "file";
    },
    //排除图片格式，判断文件格式
    showFile(str) {
      const imgRegex = new RegExp(this.imgDict, "i");
      return !str?.match(imgRegex);
    },
    //附件预览
    handlePreview(index) {
      this.showViewer = true;
      this.previewIndex = index;
    },
    //附件中文件单个下载
    async handleDownLoad(row) {
      const res = await downLoadUrl2Blob({ fileUrl: row[this.options.url] });
      if (res) {
        await fileDownLoad(res, row[this.options.name]);
      }
    },
  },
};
</script>

<style></style>
