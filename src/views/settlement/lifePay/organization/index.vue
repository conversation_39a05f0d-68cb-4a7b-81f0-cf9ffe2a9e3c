<!-- 机构信息 -->
<template>
  <div class="card-container">
    <BuseCrud
      ref="crud"
      :loading="loading"
      :filterOptions="filterOptions"
      :tablePage="tablePage"
      :pagerProps="pagerProps"
      :tableColumn="tableColumn"
      :tableData="tableData"
      :tableProps="tableProps"
      :modalConfig="modalConfig"
      @modalConfirm="modalConfirmHandler"
      @loadData="loadData"
      @rowEdit="rowEdit"
    >
      <template slot="filterCustomBtn">
        <div class="btn-wrap">
          <!-- <el-button
            type="primary"
            icon="el-icon-download"
            @click.stop="handleExport"
            v-has-permi="['serviceProvider:list:export']"
            >导出
          </el-button> -->
          <el-button
            type="primary"
            icon="el-icon-search"
            @click.stop="handleQuery"
            >查询
          </el-button>
          <el-button icon="el-icon-refresh" @click.stop="handleReset"
            >重置
          </el-button>
        </div>
      </template>
      <template #toolbar_buttons>
        <el-button
          type="primary"
          @click="handleBatchAdd"
          v-has-permi="['lifePay:organization:import']"
          >导入</el-button
        >
      </template>
      <template slot="upload" slot-scope="{ row, operationType }"> </template>
      <template slot="download" slot-scope="{ row, operationType }"> </template>
    </BuseCrud>
    <BatchUpload
      @uploadSuccess="handleQuery"
      ref="batchUpload"
      title="批量导入机构信息"
      :uploadApi="uploadObj.api"
      :templateUrl="uploadObj.url"
      :extraData="uploadObj.extraData"
      :maxSize="0.01953"
      maxSizeText="20m"
    >
    </BatchUpload>
  </div>
</template>

<script>
import checkPermission from "@/utils/permission.js";
import api from "@/api/settlement/lifePay/organization/index.js";
import { initParams } from "@/utils/buse.js";
import exportMixin from "@/mixin/export.js";
import moment from "moment";
import BatchUpload from "@/components/BatchUpload/index.vue";
import { queryLog } from "@/api/common.js";
import Timeline from "@/components/Timeline/index.vue";

export default {
  name: "lifePayOrganization",
  components: { BatchUpload, Timeline },
  mixins: [exportMixin],
  data() {
    return {
      uploadObj: {
        api: "/st/lifePay/institInfo/importExcel",
        url: "/charging-maintenance-ui/static/机构信息导入模板.xlsx",
        extraData: {},
      },
      tableTotal: 0,
      // 查询参数
      searchForm: {
        pageNum: 1,
        pageSize: 10,
        recordId: "",
      },
      visible: false,
      //buse参数-s
      tableProps: {
        border: true,
        align: "center",
        resizable: true,
        showOverflow: "tooltip",
        toolbarConfig: {
          custom: true,
          slots: {
            buttons: "toolbar_buttons",
          },
        },
        rowConfig: {
          keyField: "agingId",
          isCurrent: true,
        },
        checkboxConfig: {
          // checkRowKeys: selectRowsId,
          reserve: true,
        },
      },
      tableData: [],

      params: {},
      loading: false,
      pagerProps: {
        layouts: [
          "Total",
          "Sizes",
          "PrevPage",
          "JumpNumber",
          "NextPage",
          "FullJump",
        ],
        background: true,
        className: "pagination-container",
        pageSizes: [10, 20, 30, 50],
      },
      tablePage: {
        total: 0,
        currentPage: 1,
        pageSize: 10,
      },
      operationType: "editStatus",
      //buse参数-e

      businessTypeOptions: [],
      onlineStatusOptions: [],
      serviceProviderOptions: [],
      institutionNameOptions: [],
    };
  },
  created() {
    this.params = {
      ...initParams(this.filterOptions.config),
    };
    // 获取业务类型字典
    this.getDicts("st_business_type").then((response) => {
      this.businessTypeOptions = response.data;
    });

    // 获取上线状态字典
    this.getDicts("st_online_status").then((response) => {
      this.onlineStatusOptions = response.data;
    });

    // 获取下拉列表数据
    api.getDropLists().then((res) => {
      if (res.success) {
        // 处理下拉列表数据
        if (res.data.institutionName) {
          this.institutionNameOptions = res.data.institutionName.map(
            (item) => ({
              dictLabel: item,
              dictValue: item,
            })
          );
        }
        if (res.data.serviceProvider) {
          this.serviceProviderOptions = res.data.serviceProvider.map(
            (item) => ({
              dictLabel: item,
              dictValue: item,
            })
          );
        }
      }
    });

    // this.loadData();
  },
  methods: {
    checkPermission,

    handleBatchAdd() {
      this.$refs.batchUpload.open();
    },

    rowEdit(row) {
      this.$refs.crud.switchModalView(true, "UPDATE", {
        ...initParams(this.modalConfig.formConfig),
        ...row,
      });
    },
    handleDownload(row) {},
    handleUpload(row) {},
    handleExport() {
      //数据是双向绑定的，所以直接获取data中定义的params即可获取到所有筛选项的值
      let params = {
        ...this.params,
        // pageNum: this.tablePage.currentPage,
        // pageSize: this.tablePage.pageSize,
      };

      this.handleTimeRange(params);
      this.handleCommonExport(api.export, params);
    },
    //处理时间范围参数
    handleTimeRange(params) {
      // 机构信息页面暂无时间范围参数
    },
    async loadData() {
      //数据是双向绑定的，所以直接获取data中定义的params即可获取到所有筛选项的值
      let params = {
        ...this.params,
        pageNum: this.tablePage.currentPage,
        pageSize: this.tablePage.pageSize,
      };

      this.handleTimeRange(params);
      this.loading = true;
      const res = await api.list(params);
      this.loading = false;
      this.tableData = res.data;
      this.tablePage.total = res.total;
    },
    handleReset() {
      this.tablePage = {
        total: 0,
        currentPage: 1,
        pageSize: 10,
      };
      this.params = initParams(this.filterOptions.config);
      this.loadData();
    },
    handleQuery() {
      this.tablePage.currentPage = 1;
      this.loadData();
    },
    //弹窗确认按钮事件
    async modalConfirmHandler({ crudOperationType, ...formParams }) {
      let params = { ...formParams };
      console.log(crudOperationType, formParams, "提交");
      // crudOperationType:update

      const res = await api.update(params);
      if (res?.code === "10000") {
        this.$message.success("提交成功");
        this.loadData();
      } else {
        return false;
      }
    },
    deleteRowHandler(row) {
      this.$confirm("是否确认删除？", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then((res) => {
        let params = {
          id: row.id,
        };
        api.delete(params).then((res) => {
          if (res?.code === "10000") {
            this.$message.success("删除成功");
            this.loadData();
          }
        });
      });
    },
  },
  computed: {
    tableColumn() {
      return [
        {
          field: "institutionName",
          title: "机构名称",
          width: 180,
        },
        {
          field: "institutionCode",
          title: "机构编码",
          width: 120,
        },
        {
          field: "billingOrgCode",
          title: "出账机构编码",
          width: 120,
        },
        {
          field: "settlementInstitutionName",
          title: "结算机构名称",
          width: 120,
        },
        {
          field: "settlementPid",
          title: "结算PID",
          width: 120,
        },
        {
          field: "businessType",
          title: "业务类型",
          width: 120,
        },
        {
          field: "writeOffOrg",
          title: "销账机构",
          width: 120,
        },
        {
          field: "primaryServiceProvider",
          title: "一级服务商",
          width: 120,
        },
        {
          field: "secondaryServiceProvider",
          title: "二级服务商",
          width: 120,
        },
        {
          field: "onlineStatus",
          title: "上线状态",
          width: 120,
        },
        {
          field: "auditStatus",
          title: "审核状态",
          width: 120,
        },
        {
          field: "onlineTime",
          title: "上线时间",
          width: 120,
        },
        {
          field: "settlementRequirements",
          title: "结算单要求",
          width: 120,
        },
        {
          field: "collectionRequirements",
          title: "收据要求",
          width: 120,
        },
        {
          field: "handlingFee",
          title: "手续函",
          width: 120,
        },
        {
          field: "collectionReceipt",
          title: "收据函",
          width: 120,
        },
        {
          field: "alipayBd",
          title: "支付宝BD",
          width: 120,
        },
      ];
    },
    filterOptions() {
      return {
        showCount: 5, //默认显示筛选项的个数 多余的展开展示
        layout: "right",
        inline: true,
        labelWidth: "140px",
        //筛选控件配置
        config: [
          {
            field: "institutionName",
            element: "el-input",
            title: "机构名称",
          },
          {
            field: "businessType",
            title: "业务类型",
            element: "el-select",
            props: {
              options: this.businessTypeOptions,
              optionLabel: "dictLabel",
              optionValue: "dictValue",
              filterable: true,
            },
          },
          {
            field: "onlineStatus",
            title: "上线状态",
            element: "el-select",
            props: {
              options: this.onlineStatusOptions,
              optionLabel: "dictLabel",
              optionValue: "dictValue",
              filterable: true,
            },
          },
          {
            field: "serviceProvider",
            element: "el-select",
            title: "服务商",
            props: {
              options: this.serviceProviderOptions,
              optionLabel: "dictLabel",
              optionValue: "dictValue",
              filterable: true,
            },
          },
        ],
        params: this.params,
      };
    },

    modalConfig() {
      return {
        // modalFullScreen: true,
        viewBtn: false,
        submitBtn: false,
        okText: "确认",
        cancelText: "取消",
        addBtn: false,
        editBtn: checkPermission(["lifePay:organization:edit"]),
        delBtn: false,
        menu: true,
        menuWidth: 250,
        menuFixed: "right",
        modalWidth: "50%",
        formConfig: [
          {
            field: "settlementRequirements",
            title: "结算单要求",
            element: "el-input",
            attrs: {
              maxlength: 100,
              placeholder: "100个字符以内",
            },
          },
          {
            field: "collectionRequirements",
            title: "收据要求",
            element: "el-input",
            attrs: {
              maxlength: 100,
              placeholder: "100个字符以内",
            },
          },
          {
            field: "handlingFee",
            title: "手续函",
            element: "el-input",
            attrs: {
              maxlength: 100,
              placeholder: "100个字符以内",
            },
          },
          {
            field: "handlingFeeUrl",
            title: "手续函URL",
            element: "el-input",
            attrs: {
              maxlength: 200,
              placeholder: "200个字符以内",
            },
          },
          {
            field: "collectionReceipt",
            title: "收据函",
            element: "el-input",
            attrs: {
              maxlength: 100,
              placeholder: "100个字符以内",
            },
          },
          {
            field: "collectionReceiptUrl",
            title: "收据函URL",
            element: "el-input",
            attrs: {
              maxlength: 200,
              placeholder: "200个字符以内",
            },
          },
        ],

        crudPermission: [],
        customOperationTypes: [
          {
            title: "上传",
            typeName: "upload",
            slotName: "upload",
            showForm: false,
            event: (row) => {
              return this.handleUpload(row);
            },
            condition: (row) => {
              return checkPermission(["lifePay:organization:upload"]);
            },
          },
          {
            title: "下载",
            typeName: "download",
            slotName: "download",
            showForm: false,
            event: (row) => {
              return this.handleDownload(row);
            },
            condition: (row) => {
              return checkPermission(["lifePay:organization:download"]);
            },
          },
        ],
        formLayoutConfig: {
          defaultColSpan: 24,
          labelPosition: "right",
          labelWidth: "110px",
        },
      };
    },
  },
};
</script>

<style></style>
