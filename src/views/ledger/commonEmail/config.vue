<template>
  <div v-loading="loading">
    <DynamicForm
      ref="baseForm"
      :config="config"
      :params="formParams"
      labelPosition="right"
      :defaultColSpan="24"
      labelWidth="150px"
      @submit.prevent
    >
      <template #stationTable>
        <LuckySheet
          v-model="formParams.stationConfigDetails"
          :containerId="'luckysheet-config-' + uniqueId"
          ref="luckySheet"
        />
      </template>
      <template #mailReplyContent>
        <Editor v-model="formParams.mailReplyContent" />
      </template>
      <template #activeType>
        <div style="display: flex; align-items: center;">
          <el-select
            v-model="formParams.activeType"
            placeholder="请选择活动类型"
            style="width: 200px;"
            filterable
            clearable
          >
            <el-option
              v-for="item in activeTypeOptions"
              :key="item.dictValue"
              :label="item.dictLabel"
              :value="item.dictValue"
            ></el-option>
          </el-select>
          <div style="display: flex; align-items: center; margin-left: 10px;">
            <el-input-number
              v-model="formParams.activeTypeCount"
              :min="0"
              :precision="0"
              controls-position="right"
              style="width: 120px;"
            ></el-input-number>
            <span style="margin-left: 5px;">次</span>
          </div>
        </div>
      </template>
    </DynamicForm>
    <div class="drawer-btn">
      <el-button @click="handleClose" size="medium">取 消</el-button>
      <el-button
        @click="handleSubmit"
        type="primary"
        size="medium"
        :loading="btnLoading"
        >确 定</el-button
      >
    </div>
  </div>
</template>

<script>
import { queryCityTree } from "@/api/common.js";
import api from "@/api/ledger/commonEmail.js";
import { initParams } from "@/utils/buse.js";
import Editor from "@/components/Editor/index.vue";
import LuckySheet from "@/components/LuckySheet/index.vue";

export default {
  components: { Editor, LuckySheet },
  data() {
    return {
      baseData: {},
      mailDataId: "",
      differenceTypeOptions: [],
      formParams: {},
      btnLoading: false,
      loading: false,
      activeTypeOptions: [],
      uniqueId: Date.now() + Math.floor(Math.random() * 1000),
    };
  },
  computed: {
    config() {
      return [
        {
          field: "stationConfigDetails",
          title: "配置结果",
          element: "slot",
          slotName: "stationTable",
          defaultValue: [],
          rules: [
            {
              required: true,
              trigger: "change",
              message: "场站配置明细不能为空",
            },
          ],
        },
        {
          field: "activeType",
          title: "活动类型",
          element: "slot",
          slotName: "activeType",
          rules: [
            { required: true, trigger: "change", message: "活动类型不能为空" },
          ],
          defaultValue: "1",
        },
        {
          field: "activeTypeCount",
          title: "",
          show: false,
          defaultValue: "1",
        },
        {
          field: "stationShareCount",
          title: "场站分润（次）",
          element: "el-input-number",
          props: {
            max: 100,
            min: 0,
            precision: 0,
          },
          rules: [
            { required: true, trigger: "change", message: "场站分润不能为空" },
          ],
          defaultValue: 1,
        },
        {
          field: "configInstructions",
          title: "配置说明",
          props: {
            type: "textarea",
          },
          attrs: {
            rows: 5,
            maxlength: 500,
            showWordLimit: true,
            placeholder: "请输入具体的描述，500个字符以内",
          },
        },
        {
          field: "attachmentFileList",
          title: "上传",
          element: "file-upload",
          props: {
            limit: 20,
            accept: ".jpg, .jpeg, .png",
            fileMaxSize: 50,
            textTip: "支持批量上传，上传格式为jpg、jpeg、png文件",
          },
        },
      ];
    },
  },
  created() {
    this.formParams = initParams(this.config);
    this.getCityRegionData();
    this.getDicts("difference_type").then((response) => {
      this.differenceTypeOptions = response.data;
    });
    this.getDicts("activity_type").then((response) => {
      this.activeTypeOptions = response.data;
    });

    // 从路由参数获取数据
    const { mailId, messageId } = this.$route.query;
    this.messageId = messageId;
    this.mailDataId = mailId;
    if (mailId) {
      this.getDetail();
    }
  },
  methods: {
    handleSubmit() {
      this.$refs.baseForm.validate((valid) => {
        if (!valid) return false;

        const { region, stationConfigDetails } = { ...this.formParams };
        let params = {
          ...this.formParams,
          provinceCode: region?.[0] ? region?.[0] + "0000" : undefined,
          cityCode: region?.[1] ? region?.[1] + "00" : undefined,
          mailDataId: this.mailDataId,
          messageId: this.messageId,
          stationConfigDetails: JSON.stringify(stationConfigDetails),
        };

        this.btnLoading = true;
        api
          .saveConfig(params)
          .then(() => {
            this.$message.success("保存成功");
            this.handleClose();
          })
          .finally(() => {
            this.btnLoading = false;
          });
      });
    },

    handleClose() {
      this.$store.dispatch("tagsView/delView", this.$route);
      this.$router.push("/ledger/commonEmail");
    },

    getDetail() {
      this.loading = true;
      api
        .getConfigDetail({ mailDataId: this.mailDataId })
        .then((res) => {
          const { provinceCode, cityCode, stationConfigDetails } = res.data;
          this.formParams = {
            ...this.formParams,
            ...res.data,
            region:
              provinceCode && cityCode
                ? [provinceCode.slice(0, -4), cityCode.slice(0, -2)]
                : [],
            stationConfigDetails: stationConfigDetails
              ? JSON.parse(stationConfigDetails)
              : [],
          };
        })
        .finally(() => {
          this.loading = false;
        });
    },

    getCityRegionData() {
      queryCityTree({}).then((res) => {
        this.regionData = this.cleanTree(res.data);
      });
    },

    cleanTree(arr) {
      return arr.map((item) => {
        const newItem = { ...item };
        if (newItem.children) {
          newItem.children = this.cleanTree(newItem.children);
          if (newItem.children.length === 0) {
            delete newItem.children;
          }
        }
        return newItem;
      });
    },
  },
};
</script>

<style lang="less" scoped>
.drawer-btn {
  display: flex;
  justify-content: center;
  margin-top: 20px;
  .el-button + .el-button {
    margin-left: 40px;
  }
  .el-button--medium {
    font-size: 16px;
    border-radius: 4px;
    padding: 14px 26px;
  }
}
</style>
