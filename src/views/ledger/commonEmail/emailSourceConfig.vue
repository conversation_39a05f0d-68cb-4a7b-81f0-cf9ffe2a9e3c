<!-- 邮箱数据源配置 -->
<template>
  <div>
    <BuseCrud
      ref="crud"
      :loading="loading"
      :tablePage="tablePage"
      :pagerProps="pagerProps"
      :tableColumn="tableColumn"
      :tableData="tableData"
      :tableProps="tableProps"
      :modalConfig="modalConfig"
      @modalConfirm="modalConfirmHandler"
      @loadData="loadData"
      @rowEdit="handleEdit"
    >
      <template slot="toolbar_buttons">
        <div class="btn-wrap">
          <el-button
            type="primary"
            icon="el-icon-plus"
            @click.stop="handleAdd"
            v-has-permi="['commonEmail:sourceConfig:add']"
            >新增
          </el-button>
        </div>
      </template>
      <template #log>
        <Timeline
          :list="logList"
          operateTypeTitle="operatorTypeName"
          operatorNameTitle="operatorUserName"
          createTimeTitle="operatorTime"
          operateDetailTitle="remark"
        ></Timeline>
      </template>
      <template #modalFooter="{ crudOperationType }">
        <div v-if="crudOperationType === 'log'"></div>
      </template>
      <template #emailSignature="{ item, params }">
        <Editor v-model="params.sign" ref="quillEditor" class="email-editor" />
      </template>

      <template slot="status" slot-scope="{ row }">
        <el-switch
          v-model="row.status"
          @change="handleStatusChange(row)"
          active-value="01"
          inactive-value="02"
          v-loading="statusLoading"
          :disabled="!checkPermission(['commonEmail:sourceConfig:status'])"
        />
      </template>
    </BuseCrud>
  </div>
</template>

<script>
import checkPermission from "@/utils/permission.js";
import api from "@/api/ledger/commonEmail.js";
import { initParams } from "@/utils/buse.js";
import Timeline from "@/components/Timeline/index.vue";
import Editor from "@/components/Editor/index.vue";
import moment from "moment";
export default {
  name: "emailSourceConfig",
  components: {
    Timeline,
    Editor,
  },
  data() {
    return {
      //buse参数-s
      tableProps: {
        border: true,
        align: "center",
        resizable: true,
        showOverflow: "tooltip",
        rowConfig: {
          keyField: "id",
          isCurrent: true,
        },
        checkboxConfig: {
          reserve: true,
        },
        toolbarConfig: {
          custom: true,
          slots: {
            buttons: "toolbar_buttons",
          },
        },
      },
      tableData: [],
      params: {},
      loading: false,
      pagerProps: {
        layouts: [
          "Total",
          "Sizes",
          "PrevPage",
          "JumpNumber",
          "NextPage",
          "FullJump",
        ],
        background: true,
        className: "pagination-container",
        pageSizes: [10, 20, 30, 50],
      },
      tablePage: {
        total: 0,
        currentPage: 1,
        pageSize: 10,
      },
      operationType: "",
      //buse参数-e
      logList: [], // 日志列表
      emailSourceTypeOptions: [], // 邮件来源类型选项
      statusLoading: false, // 状态切换加载标志
    };
  },
  created() {
    this.loadData();
    this.getEmailSourceTypes();
  },
  methods: {
    checkPermission,
    // 成功提示
    msgSuccess(message) {
      this.$message.success(message);
    },
    // 获取邮件来源类型选项
    async getEmailSourceTypes() {
      // 模拟API调用获取邮件来源类型
      setTimeout(() => {
        this.emailSourceTypeOptions = [
          { value: "公司邮箱", label: "公司邮箱" },
          { value: "个人邮箱", label: "个人邮箱" },
          { value: "客户邮箱", label: "客户邮箱" },
        ];
      }, 300);
    },
    // 新增
    handleAdd() {
      this.operationType = "add";
      this.$refs.crud.switchModalView(true, "ADD");
    },
    // 编辑
    handleEdit(row) {
      this.operationType = "edit";
      // 准备编辑表单数据，处理日期范围
      const formData = {
        ...initParams(this.modalConfig.formConfig),
        ...row,
        // 设置日期范围字段
        dateRange:
          row.timeConfig == "custom" && row.startTime && row.endTime
            ? [row.startTime, row.endTime]
            : undefined,
      };
      this.$refs.crud.switchModalView(true, "UPDATE", formData);
    },
    // 查看日志
    async handleLog(row) {
      this.logList = []; // 清空日志列表
      this.operationType = "log";
      this.$refs.crud.switchModalView(true, "log", {});

      try {
        // 调用获取操作记录的API
        const res = await api.queryRecordList({
          businessId: row.id,
          businessType: "MAIL_SOURCE_CONFIG", // 业务类型，根据实际情况调整
        });

        if (res && res.success && res.data) {
          this.logList = res.data;
        } else {
          this.$message.warning("暂无操作记录");
        }
      } catch (error) {
        console.error("获取操作记录失败", error);
        this.$message.error("获取操作记录失败，请稍后重试");
      }
    },
    // 关闭日志弹窗
    handleCloseLog() {
      this.$refs.crud.switchModalView(false);
    },
    // 启用/禁用
    handleStatusChange(row) {
      // 如果是停用操作，需要先确认
      if (row.status === "02") {
        this.$confirm("是否确认停用？", "提示", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning",
        })
          .then(() => {
            this.updateStatus(row);
          })
          .catch(() => {
            // 用户取消操作，恢复原状态
            row.status = row.status === "01" ? "02" : "01";
          });
      } else {
        // 启用操作直接执行
        this.updateStatus(row);
      }
    },

    // 更新状态
    async updateStatus(row) {
      this.statusLoading = true;
      try {
        // 调用API修改状态
        const res = await api.updateMailSourceConfigStatus({
          id: row.id,
          status: row.status,
        });

        if (res && res.success) {
          this.msgSuccess("操作成功");
          // 刷新数据
          this.loadData();
        } else {
          // 操作失败，恢复原状态
          row.status = row.status === "01" ? "02" : "01";
          this.$message.error(res.message || "操作失败");
        }
      } catch (error) {
        console.error("修改状态失败", error);
        // 操作失败，恢复原状态
        row.status = row.status === "01" ? "02" : "01";
        this.$message.error("操作失败，请稍后重试");
      } finally {
        this.statusLoading = false;
      }
    },
    // 邮箱校验
    validateEmail(_, value, callback) {
      if (!value) {
        callback(new Error("请输入邮箱地址"));
      } else if (!/^\w+([-+.]\w+)*@\w+([-.]\w+)*\.\w+([-.]\w+)*$/.test(value)) {
        callback(new Error("请输入正确的邮箱地址"));
      } else {
        callback();
      }
    },

    async loadData() {
      this.loading = true;
      try {
        // 构建查询参数
        const params = {
          pageNum: this.tablePage.currentPage,
          pageSize: this.tablePage.pageSize,
        };

        // 调用API获取数据
        const res = await api.getMailSourceConfigList(params);

        if (res && res.success) {
          // 直接使用接口返回的数据，不做额外处理
          this.tableData = res.data || [];

          this.tablePage.total = res.total || 0;
        } else {
          this.$message.error(res.message || "获取数据失败");
          this.tableData = [];
          this.tablePage.total = 0;
        }
      } catch (error) {
        console.error("获取邮件数据源配置列表失败", error);
        this.$message.error("获取数据失败，请稍后重试");
        this.tableData = [];
        this.tablePage.total = 0;
      } finally {
        this.loading = false;
      }
    },
    //弹窗确认按钮事件
    async modalConfirmHandler({ crudOperationType, ...formParams }) {
      let params = { ...formParams };

      // 处理富文本编辑器内容
      if (this.$refs.quillEditor) {
        params.sign = this.$refs.quillEditor.fontClassToStyle(
          params.sign || ""
        );
      }

      // 处理日期范围
      if (params.timeConfig === "custom" && params.dateRange) {
        params.startTime = params.dateRange[0];
        params.endTime = params.dateRange[1];
      } else if (params.timeConfig === "unlimited") {
        params.startTime = moment().format("YYYY-MM-DD");
        params.endTime = undefined;
      }

      // 删除临时字段，只保留接口需要的字段
      delete params.dateRange;

      console.log("提交参数：", params, crudOperationType);
      try {
        const res = await api[crudOperationType](params);

        if (res && res.success) {
          this.$message.success("提交成功");
          this.loadData();
          return true;
        } else {
          this.$message.error(res.message || "提交失败");
          return false;
        }
      } catch (error) {
        console.error("提交邮件数据源配置失败", error);
        this.$message.error("提交失败，请稍后重试");
        return false;
      }
    },
  },
  computed: {
    tableColumn() {
      return [
        {
          field: "mailAddr", // 修改为接口字段名
          title: "接收邮箱",
        },
        {
          field: "password", // 修改为接口字段名
          title: "登录密码",
        },
        {
          field: "timeConfig", // 使用原始字段
          title: "接收时间范围",
          formatter: ({ cellValue, row }) => {
            if (cellValue === "unlimited") {
              return `${row.startTime} ~ 无限期`;
            } else {
              return `${row.startTime} ~ ${row.endTime}`;
            }
          },
          width: 230,
        },
        {
          field: "mailSource", // 修改为接口字段名
          title: "邮件来源类型",
        },
        {
          field: "createName", // 修改为接口字段名
          title: "配置人",
        },
        {
          field: "createTime", // 修改为接口字段名
          title: "配置时间",
        },
        {
          field: "status",
          title: "状态",
          slots: {
            default: "status",
          },
        },
      ];
    },
    modalConfig() {
      return {
        viewBtn: false,
        submitBtn: false,
        okText: "确认",
        cancelText: "取消",
        addBtn: false,
        editBtn: checkPermission(["commonEmail:sourceConfig:edit"]),
        delBtn: false,
        menu: true,
        menuWidth: 250,
        menuFixed: "right",
        modalWidth: "50%",
        formConfig: [
          {
            field: "mailAddr",
            element: "el-input",
            title: "接收邮箱",
            attrs: {
              placeholder: "请输入邮箱地址，仅支持单邮箱输入",
            },
            rules: [
              { required: true, message: "请输入接收邮箱" },
              { validator: this.validateEmail },
            ],
          },
          {
            field: "password",
            element: "el-input",
            title: "登录密码",
            props: {
              type: "password",
              showPassword: true,
            },
            attrs: {
              placeholder: "请输入接收邮箱的登录密码",
            },
            rules: [{ required: true, message: "请输入登录密码" }],
          },
          {
            field: "timeConfig",
            element: "el-radio-group",
            title: "接收数据的时间范围",
            props: {
              options: [
                { value: "custom", label: "自定义" },
                { value: "unlimited", label: "无限期" },
              ],
            },
            rules: [{ required: true, message: "请选择接收时间范围类型" }],
            defaultValue: "custom",
          },
          {
            field: "dateRange",
            element: "el-date-picker",
            title: "",
            colSpan: { span: 24, offset: 2 },
            props: {
              type: "daterange",
              valueFormat: "yyyy-MM-dd",
              rangeSeparator: "至",
              startPlaceholder: "开始日期",
              endPlaceholder: "结束日期",
            },
            rules: [
              {
                required:
                  this.$refs.crud?.getFormFields()?.timeConfig === "custom",
                message: "请选择接收时间范围",
              },
            ],
            show: () =>
              this.$refs.crud?.getFormFields()?.timeConfig === "custom",
          },
          {
            field: "mailSource",
            element: "el-autocomplete",
            title: "邮件来源类型",
            props: {
              placeholder: "请输入或下拉选择",
              fetchSuggestions: (queryString, cb) => {
                const results = queryString
                  ? this.emailSourceTypeOptions.filter((item) =>
                      item.value
                        .toLowerCase()
                        .includes(queryString.toLowerCase())
                    )
                  : this.emailSourceTypeOptions;
                cb(results);
              },
              valueKey: "value",
            },
            rules: [{ required: true, message: "请输入或选择邮件来源类型" }],
          },
          {
            field: "status",
            element: "el-radio-group",
            title: "状态",
            props: {
              options: [
                { value: "01", label: "启用" },
                { value: "02", label: "停用" },
              ],
            },
            defaultValue: "01",
          },
          {
            field: "sign",
            element: "slot",
            slotName: "emailSignature",
            title: "签名设置",
          },
        ],
        crudPermission: [],
        customOperationTypes: [
          {
            title: "日志",
            typeName: "log",
            slotName: "log",
            showForm: false,
            event: (row) => {
              this.handleLog(row);
            },
            condition: () => {
              return checkPermission(["commonEmail:sourceConfig:log"]);
            },
          },
        ],
        formLayoutConfig: {
          defaultColSpan: 24,
          labelPosition: "right",
          labelWidth: "150px",
        },
      };
    },
  },
};
</script>

<style lang="less" scoped>
.email-editor {
  height: 300px;
  /deep/ .ql-container {
    max-height: 250px;
    height: 250px;
  }
}
</style>
