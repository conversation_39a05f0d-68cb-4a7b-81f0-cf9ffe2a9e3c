//自定义工序项统计
<template>
  <div style="padding: 0 20px;">
    <BuseCrud
      ref="crud"
      :loading="loading"
      :filterOptions="filterOptions"
      :tablePage="tablePage"
      :pagerProps="pagerProps"
      :tableColumn="tableColumn"
      :tableData="tableData"
      :tableProps="tableProps"
      :modalConfig="modalConfig"
      @loadData="loadData"
      tabType="card"
    >
      <template slot="filterCustomBtn">
        <div class="btn-wrap">
          <el-button
            type="primary"
            icon="el-icon-download"
            @click.stop="handleExport"
            v-has-permi="['ledger:dashboard:processItemExport']"
            >导出
          </el-button>
          <el-button
            type="primary"
            icon="el-icon-search"
            @click.stop="handleQuery"
            >查询
          </el-button>
          <el-button icon="el-icon-refresh" @click.stop="handleReset"
            >重置
          </el-button>
        </div>
      </template>
    </BuseCrud>
  </div>
</template>

<script>
import { getToken } from "@/utils/auth";
import { initParams } from "@/utils/buse.js";
import { queryTreeList } from "@/api/ledger/businessType.js";
import { queryDeptOrderTree } from "@/api/ledger/workOrderType.js";
import { listAllUser } from "@/api/common.js";

import api from "@/api/ledger/dashboard.js";
import checkPermission from "@/utils/permission.js";
import exportMixin from "@/mixin/export.js";

export default {
  components: {},
  mixins: [exportMixin],
  props: {},
  data() {
    return {
      summary: {},
      token: "",
      //buse参数-s
      tableProps: {
        border: true,
        align: "center",
        resizable: true,
        showOverflow: "tooltip",
        rowConfig: {
          keyField: "id",
          isCurrent: true,
        },
        checkboxConfig: {
          reserve: true,
        },
        footerMethod: this.footerMethod,
        showFooter: true,
        // 设置表尾合计行固定在底部
        footerConfig: {
          fixed: true, // 固定表尾
          background: "#f8f8f9", // 背景色
          height: 40, // 高度
        },
        footerRowClassName: "footer-row-summary",
      },
      tableData: [],
      params: {},
      loading: false,
      pagerProps: {
        layouts: [
          "Total",
          "Sizes",
          "PrevPage",
          "JumpNumber",
          "NextPage",
          "FullJump",
        ],
        background: true,
        className: "pagination-container",
        pageSizes: [10, 20, 30, 50],
      },
      tablePage: {
        total: 0,
        currentPage: 1,
        pageSize: 10,
      },
      //buse参数-e
      businessTypeOptions: [],
      orderTypeOptions: [],
      supportDeptOptions: [],
      userOptions: [],
      processItemOptions: [],
      nodeNameOptions: [],
      deptOptions: [],
    };
  },
  created() {
    this.token = getToken();
    this.params = initParams(this.filterOptions.config);
    this.listAllUser();
    queryDeptOrderTree({}).then((res) => {
      this.orderTypeOptions = res.data?.map((x) => {
        return { ...x, disabled: true };
      });
    });
    queryTreeList({}).then((res) => {
      this.businessTypeOptions = res.data;
    });
    // 获取工序项名称选项
    this.getProcessNameOptions();
    // 获取节点名称选项
    this.getNodeNameOptions();
  },
  mounted() {
    Promise.all([
      this.getDicts("support_dept").then((response) => {
        this.supportDeptOptions = response.data;
        this.deptOptions = response.data;
      }),
    ]).then(() => {
      setTimeout(() => {
        this.$nextTick(() => {
          this.loadData();
        });
      }, 500);
    });
  },
  computed: {
    modalConfig() {
      return {
        menu: false,
        addBtn: false,
        viewBtn: false,
        editBtn: false,
        delBtn: false,
        menuWidth: 160,
        menuFixed: "right",
        //自定义操作按钮
        customOperationTypes: [],
      };
    },
    tableColumn() {
      return [
        {
          field: "processName",
          title: "工序项名称",
        },
        {
          field: "orderNo",
          title: "工单编号",
          width: 180,
          slots: {
            default: ({ row }) => {
              return [
                <span
                  style="color: #029c7c; cursor: pointer;"
                  onClick={() => this.jumpToDetail(row)}
                >
                  {row.orderNo}
                </span>,
              ];
            },
          },
        },
        {
          field: "orderTypeStr",
          title: "工单类型",
        },
        {
          field: "businessTypeStr",
          title: "业务类型",
        },
        {
          field: "nodeName",
          title: "节点名称",
        },

        {
          field: "handleTimeStr",
          title: "处理时间",
          width: 150,
        },
        {
          field: "handlerUserName",
          title: "处理人",
        },
        {
          field: "deptName",
          title: "处理人所属部门",
        },

        {
          field: "processTime",
          title: "标准工时(h)",
          width: 100,
        },
        {
          field: "processCount",
          title: "工序个数(个)",
          width: 100,
        },
        {
          field: "totalTime",
          title: "计算工时(h)",
          width: 100,
        },
      ];
    },
    filterOptions() {
      return {
        showCount: 5, //默认显示筛选项的个数 多余的展开展示
        layout: "right",
        inline: true,
        labelWidth: "140px",
        //筛选控件配置
        config: [
          {
            field: "processNameList",
            title: "工序项名称",
            element: "el-select",
            props: {
              options: this.processItemOptions,
              filterable: true,
              multiple: true,
              collapseTags: true,
            },
          },
          {
            field: "businessType",
            title: "业务类型",
            element: "custom-cascader",
            attrs: {
              collapseTags: true,
              props: {
                expandTrigger: "hover",
                checkStrictly: true,
                multiple: true,
                value: "id",
                label: "typeName",
                children: "childrenList",
              },
              options: this.businessTypeOptions,
            },
          },
          {
            field: "orderTypeArr",
            title: "工单类型",
            element: "custom-cascader",
            attrs: {
              collapseTags: true,
              props: {
                expandTrigger: "hover",
                checkStrictly: true,
                multiple: true,
                value: "id",
                label: "typeName",
                children: "childrenList",
              },
              options: this.orderTypeOptions,
            },
          },
          {
            field: "handlerUsers",
            title: "处理人",
            element: "el-select",
            props: {
              options: this.userOptions,
              optionLabel: "label",
              optionValue: "value",
              filterable: true,
              multiple: true,
              collapseTags: true,
            },
          },
          {
            field: "deptIds",
            title: "处理人所属部门",
            element: "el-select",
            props: {
              options: this.deptOptions,
              optionLabel: "dictLabel",
              optionValue: "dictValue",
              filterable: true,
              multiple: true,
              collapseTags: true,
            },
          },
          {
            field: "handleTimeStr",
            title: "处理时间",
            element: "el-date-picker",
            props: {
              type: "daterange",
              valueFormat: "yyyy-MM-dd",
              pickerOptions: {
                shortcuts: [
                  {
                    text: "今日",
                    onClick(picker) {
                      const today = new Date();
                      picker.$emit("pick", [today, today]);
                    },
                  },
                  {
                    text: "昨日",
                    onClick(picker) {
                      const yesterday = new Date();
                      yesterday.setDate(yesterday.getDate() - 1);
                      picker.$emit("pick", [yesterday, yesterday]);
                    },
                  },
                  {
                    text: "本周",
                    onClick(picker) {
                      const end = new Date();
                      const start = new Date();
                      start.setDate(
                        start.getDate() -
                          start.getDay() +
                          (start.getDay() === 0 ? -6 : 1)
                      );
                      picker.$emit("pick", [start, end]);
                    },
                  },
                  {
                    text: "本月",
                    onClick(picker) {
                      const end = new Date();
                      const start = new Date();
                      start.setDate(1);
                      picker.$emit("pick", [start, end]);
                    },
                  },
                  {
                    text: "上月",
                    onClick(picker) {
                      const end = new Date();
                      const start = new Date();
                      start.setMonth(start.getMonth() - 1);
                      start.setDate(1);
                      end.setDate(0);
                      picker.$emit("pick", [start, end]);
                    },
                  },
                  {
                    text: "本季度",
                    onClick(picker) {
                      const end = new Date();
                      const start = new Date();
                      const currentQuarter = Math.floor(start.getMonth() / 3);
                      start.setMonth(currentQuarter * 3);
                      start.setDate(1);
                      picker.$emit("pick", [start, end]);
                    },
                  },
                  {
                    text: "本年",
                    onClick(picker) {
                      const end = new Date();
                      const start = new Date();
                      start.setMonth(0);
                      start.setDate(1);
                      picker.$emit("pick", [start, end]);
                    },
                  },
                ],
              },
            },
            defaultValue: [this.getFirstDayOfMonth(), this.getCurrentDay()],
          },
          {
            field: "nodeNameList",
            title: "节点名称",
            element: "el-select",
            props: {
              options: this.nodeNameOptions,
              filterable: true,
              multiple: true,
              collapseTags: true,
            },
          },
        ],
        params: this.params,
      };
    },
  },
  methods: {
    // 处理多选级联选择器数据
    processMultiCascaderData(cascaderData) {
      const result = {
        level1: [],
        level2: [],
        level3: [],
        level4: [],
      };

      // 如果数据为空，直接返回空结果
      if (!cascaderData) {
        return result;
      }

      // 处理旧数据格式（单选模式下可能是字符串或数字）
      if (!Array.isArray(cascaderData)) {
        // 如果是字符串或数字，将其视为第一级的选择
        result.level1.push(cascaderData);
        return result;
      }

      // 处理单个数组（非嵌套数组）的情况
      if (
        Array.isArray(cascaderData) &&
        cascaderData.length > 0 &&
        !Array.isArray(cascaderData[0])
      ) {
        // 如果是单个数组（如 [1, 2, 3]），将其视为一个完整的路径
        if (cascaderData.length >= 1) {
          result.level1.push(cascaderData[0]);
        }
        if (cascaderData.length >= 2) {
          result.level2.push(cascaderData[1]);
        }
        if (cascaderData.length >= 3) {
          result.level3.push(cascaderData[2]);
        }
        if (cascaderData.length >= 4) {
          result.level4.push(cascaderData[3]);
        }
        return result;
      }

      // 处理多选数据格式（嵌套数组）
      cascaderData.forEach((path) => {
        if (Array.isArray(path)) {
          // 根据路径长度确定层级
          if (path.length >= 1) {
            result.level1.push(path[0]);
          }
          if (path.length >= 2) {
            result.level2.push(path[1]);
          }
          if (path.length >= 3) {
            result.level3.push(path[2]);
          }
          if (path.length >= 4) {
            result.level4.push(path[3]);
          }
        }
      });

      return result;
    },

    footerMethod() {
      // 使用从API获取的所有页的合计数据，而不是当前页的数据计算
      return [
        [
          "合计",
          "", // 工序项名称
          "", // 工单编号
          "", // 工单类型
          "", // 业务类型
          "", // 节点名称
          "", // 处理时间
          "", // 处理人
          "", // 处理人所属部门
          "", // 标准工时
          this.summary?.totalTime || "0.00", // 计算工时总和（从API获取的所有页的合计）
        ],
      ];
    },

    //跳转至数据明细tab
    jumpToDetail(row) {
      let params = {
        orderNo: row.orderNo,
      };
      this.$emit("jump", { tab: "detail", params });
    },

    handleQuery() {
      this.tablePage.currentPage = 1;
      this.loadData();
    },

    checkPermission,
    listAllUser() {
      listAllUser({ status: "0" }).then((res) => {
        this.userOptions = res.data.map((x) => {
          return {
            ...x,
            value: x.userId,
            label: x.nickName + "-" + x.userName,
          };
        });
      });
    },

    /**
     * 获取节点名称下拉选项
     */
    getNodeNameOptions() {
      api.getNodeNameOptions({}).then((res) => {
        // 根据后端返回的数据格式进行处理
        this.nodeNameOptions =
          res.data?.map((item) => {
            return {
              value: item,
              label: item,
            };
          }) || [];
      });
    },

    /**
     * 获取工序项名称下拉选项
     */
    getProcessNameOptions() {
      api.getProcessNameOptions({}).then((res) => {
        // 根据后端返回的数据格式进行处理
        this.processItemOptions =
          res.data?.map((item) => {
            return {
              value: item,
              label: item,
            };
          }) || [];
      });
    },

    // 获取当月第一天
    getFirstDayOfMonth() {
      const today = new Date();
      today.setDate(1);
      return today.toISOString().split("T")[0];
    },

    // 获取当前日期
    getCurrentDay() {
      const today = new Date();
      return today.toISOString().split("T")[0];
    },
    async loadData(searchParams) {
      if (searchParams) {
        this.params = {
          ...initParams(this.filterOptions.config),
          ...searchParams,
        };
      }

      const { businessType, orderTypeArr, ...rest } = this.params;

      // 处理业务类型多选数据
      const businessTypeIds = this.processMultiCascaderData(businessType);

      // 处理工单类型多选数据
      const orderTypeIds = this.processMultiCascaderData(orderTypeArr);

      //数据是双向绑定的，所以直接获取data中定义的params即可获取到所有筛选项的值
      let params = {
        ...rest,
        oneBusinessTypeIds: businessTypeIds.level1 || [],
        twoBusinessTypeIds: businessTypeIds.level2 || [],
        threeBusinessTypeIds: businessTypeIds.level3 || [],
        oneOrderTypeIds: orderTypeIds.level2 || [],
        twoOrderTypeIds: orderTypeIds.level3 || [],
        threeOrderTypeIds: orderTypeIds.level4 || [],

        pageNum: this.tablePage.currentPage,
        pageSize: this.tablePage.pageSize,
      };
      this.handleTimeRange(params);
      console.log(params, "params");

      this.loading = true;

      try {
        // 获取表格数据
        const res = await api.getProcessItemTableData(params);

        if (res && res.success) {
          // 根据接口文档中的响应结构处理数据
          this.tableData = res.data || [];
          this.tablePage.total = res.total || 0;
          this.tablePage.pageNum = res.pageNum || 1;
          this.tablePage.pageSize = res.pageSize || 10;
        } else {
          this.$message.error(res.message || "获取数据失败");
          this.tableData = [];
          this.tablePage.total = 0;
        }

        // 获取汇总数据（所有页的合计）
        const summaryRes = await api.getProcessItemSummary(params);

        // 更新汇总数据
        if (summaryRes && summaryRes.success) {
          this.summary = { totalTime: summaryRes.data || 0 };
        } else {
          this.$message.error(summaryRes.message || "获取汇总数据失败");
          this.summary = {};
        }
      } catch (error) {
        console.error("加载数据失败", error);
        this.$message.error("加载数据失败");
      } finally {
        this.loading = false;
      }
    },

    async handleExport() {
      const { businessType, orderTypeArr, ...rest } = this.params;

      // 处理业务类型多选数据
      const businessTypeIds = this.processMultiCascaderData(businessType);

      // 处理工单类型多选数据
      const orderTypeIds = this.processMultiCascaderData(orderTypeArr);

      //数据是双向绑定的，所以直接获取data中定义的params即可获取到所有筛选项的值
      let params = {
        ...rest,
        oneBusinessTypeIds: businessTypeIds.level1 || [],
        twoBusinessTypeIds: businessTypeIds.level2 || [],
        threeBusinessTypeIds: businessTypeIds.level3 || [],
        oneOrderTypeIds: orderTypeIds.level2 || [],
        twoOrderTypeIds: orderTypeIds.level3 || [],
        threeOrderTypeIds: orderTypeIds.level4 || [],

        pageNum: this.tablePage.currentPage,
        pageSize: this.tablePage.pageSize,
      };
      this.handleTimeRange(params);

      this.handleCommonExport(api.exportProcessItem, params);
    },

    //处理时间范围参数
    handleTimeRange(params) {
      const arr = [
        {
          field: "handleTimeStr",
          title: "处理时间",
          startFieldName: "handleStartTime",
          endFieldName: "handleEndTime",
        },
      ];
      arr.map((x) => {
        if (Array.isArray(params[x.field])) {
          params[x.startFieldName] = params[x.field][0] + " 00:00:00";
          params[x.endFieldName] = params[x.field][1] + " 23:59:59";
          delete params[x.field];
        }
      });
    },

    handleReset() {
      this.tablePage = {
        total: 0,
        currentPage: 1,
        pageSize: 10,
      };
      this.params = initParams(this.filterOptions.config);
      this.loadData();
    },

    // 处理二进制数据下载
    handleBlobDownload(res, fileName) {
      if (!res) {
        this.$message.error("导出失败，请重试");
        return;
      }

      // 创建Blob对象
      const blob = new Blob([res], { type: "application/vnd.ms-excel" });

      // 创建下载链接
      const link = document.createElement("a");
      link.href = URL.createObjectURL(blob);
      link.download = fileName;
      link.style.display = "none";
      document.body.appendChild(link);

      // 触发下载
      link.click();

      // 清理
      URL.revokeObjectURL(link.href);
      document.body.removeChild(link);
    },
  },
};
</script>

<style lang="less" scoped>
.page-center {
  display: flex;
  align-items: center;
  justify-content: space-around;
  padding: 24px;
  background: #eff6f4;
  font-size: 14px;
  .title {
    font-size: 18px;
  }
  .count {
    font-size: 20px;
    color: red;
  }
  .unit {
    color: red;
  }
}
/deep/ .footer-row-summary {
  background: #ecf5f1;
}
</style>
